@extends('web.layouts.layout')

@section('title', __('طلب التوثيق'))

@section('page-meta')
<meta name="description" content="{{ __('طلب التوثيق في موقع حراجي - احصل على علامة التوثيق لزيادة الثقة في حسابك') }}">
<meta name="keywords" content="{{ __('حراجي, توثيق, علامة التوثيق, ثقة, حساب موثق') }}">
@endsection

@section('content')
    <div class="container mx-auto px-4">
        <!-- Breadcrumbs -->
        <div class="text-sm breadcrumbs mb-6">
            <ul>
                <li><a href="{{ route('web.index') }}">{{ __('الرئيسية') }}</a></li>
                <li><a href="{{ route('web.profile.show') }}">{{ __('الملف الشخصي') }}</a></li>
                <li>{{ __('طلب التوثيق') }}</li>
            </ul>
        </div>

        <div class="max-w-2xl mx-auto">
        <div class="bg-base-100 rounded-lg shadow-lg p-6">
            <div class="text-center mb-6">
                <div class="w-16 h-16 bg-primary/20 rounded-full flex items-center justify-center mx-auto mb-4">
                    <i class="fas fa-shield-check text-2xl text-primary"></i>
                </div>
                <h1 class="text-2xl font-bold mb-2">{{ __('طلب التوثيق') }}</h1>
                <p class="text-base-content/70">{{ __('احصل على علامة التوثيق لزيادة الثقة في حسابك') }}</p>
            </div>

            @if(session('success'))
                <div class="alert alert-success mb-4">
                    <i class="fas fa-check-circle"></i>
                    {{ session('success') }}
                </div>
            @endif

            @if(session('error'))
                <div class="alert alert-error mb-4">
                    <i class="fas fa-exclamation-circle"></i>
                    {{ session('error') }}
                </div>
            @endif

            @if(session('info'))
                <div class="alert alert-info mb-4">
                    <i class="fas fa-info-circle"></i>
                    {{ session('info') }}
                </div>
            @endif

            <form method="POST" action="{{ route('web.verification.submit') }}" enctype="multipart/form-data">
                @csrf

                <div class="form-control mb-6">
                    <label class="label">
                        <span class="label-text font-medium">{{ __('نوع المستخدم') }}</span>
                    </label>
                    <select name="user_type" class="select select-bordered w-full" required>
                        <option value="">{{ __('اختر نوع المستخدم') }}</option>
                        <option value="individual">{{ __('فرد') }}</option>
                        <option value="business">{{ __('شركة/متجر') }}</option>
                        <option value="organization">{{ __('مؤسسة') }}</option>
                    </select>
                    @error('user_type')
                        <label class="label">
                            <span class="label-text-alt text-error">{{ $message }}</span>
                        </label>
                    @enderror
                </div>

                @if(!empty($requirementsData))
                    @foreach($requirementsData as $requirement)
                        <div class="form-control mb-6">
                            <label class="label">
                                <span class="label-text font-medium">
                                    {{ $requirement['title'] }}
                                    @if($requirement['is_required'])
                                        <span class="text-error">*</span>
                                    @endif
                                </span>
                            </label>

                            @if(!empty($requirement['description']))
                                <label class="label">
                                    <span class="label-text-alt text-base-content/70">{{ $requirement['description'] }}</span>
                                </label>
                            @endif

                            @if($requirement['type'] === 'text')
                                <input type="text"
                                       name="{{ $requirement['title'] }}"
                                       class="input input-bordered w-full"
                                       @if($requirement['is_required']) required @endif>
                            @elseif($requirement['type'] === 'textarea')
                                <textarea name="{{ $requirement['title'] }}"
                                          rows="4"
                                          class="textarea textarea-bordered w-full"
                                          @if($requirement['is_required']) required @endif></textarea>
                            @elseif($requirement['type'] === 'file')
                                <input type="file"
                                       name="{{ $requirement['title'] }}"
                                       class="file-input file-input-bordered w-full"
                                       accept=".jpg,.jpeg,.png,.pdf,.doc,.docx"
                                       @if($requirement['is_required']) required @endif>
                                <label class="label">
                                    <span class="label-text-alt">{{ __('الملفات المدعومة: JPG, PNG, PDF, DOC, DOCX (حد أقصى 10 ميجابايت)') }}</span>
                                </label>
                            @elseif($requirement['type'] === 'select' && !empty($requirement['options']))
                                <select name="{{ $requirement['title'] }}"
                                        class="select select-bordered w-full"
                                        @if($requirement['is_required']) required @endif>
                                    <option value="">{{ __('اختر...') }}</option>
                                    @foreach(explode(',', $requirement['options']) as $option)
                                        <option value="{{ trim($option) }}">{{ trim($option) }}</option>
                                    @endforeach
                                </select>
                            @endif

                            @error($requirement['title'])
                                <label class="label">
                                    <span class="label-text-alt text-error">{{ $message }}</span>
                                </label>
                            @enderror
                        </div>
                    @endforeach
                @else
                    <div class="text-center py-8">
                        <div class="flex flex-col items-center">
                            <i class="fas fa-cog text-4xl text-base-content/30 mb-4"></i>
                            <p class="text-base-content/70">{{ __('لم يتم تكوين متطلبات التوثيق بعد. يرجى المحاولة لاحقاً.') }}</p>
                        </div>
                    </div>
                @endif

                @if(!empty($requirementsData))
                    <div class="flex justify-between items-center pt-6 border-t border-base-300">
                        <a href="{{ route('web.profile.show') }}" class="btn btn-ghost">
                            <i class="fas fa-arrow-right mr-2 rtl:ml-2 rtl:mr-0"></i>
                            {{ __('العودة للملف الشخصي') }}
                        </a>
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-paper-plane mr-2 rtl:ml-2 rtl:mr-0"></i>
                            {{ __('إرسال طلب التوثيق') }}
                        </button>
                    </div>
                @endif
            </form>
        </div>
        </div>
    </div>
</main>
@endsection
