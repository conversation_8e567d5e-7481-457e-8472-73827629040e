@extends('web.layouts.layout')
@section('title', __('الملف الشخصي'))

@section('page-meta')
<meta name="description" content="{{ __('الملف الشخصي في موقع حراجي') }}">
<meta name="keywords" content="{{ __('حراجي, ملف شخصي, إعلانات, بيع, شراء') }}">
@endsection

@section('content')
<main class="py-8">
    <div class="container mx-auto px-4">
        <!-- Breadcrumbs -->
        <div class="text-sm breadcrumbs mb-6">
            <ul>
                <li><a href="{{ route('web.index') }}">{{ __('الرئيسية') }}</a></li>
                <li>{{ __('الملف الشخصي') }}</li>
            </ul>
        </div>

        <!-- Profile Header -->
        <div class="bg-base-100 rounded-lg shadow-md overflow-hidden mb-8">
            <!-- Cover Photo -->
            <div class="relative h-48 bg-base-200">
                @if($user->cover)
                    <img src="{{ asset('storage/' . $user->cover) }}" alt="{{ $user->name }}" class="w-full h-full object-cover">
                @else
                    <div class="w-full h-full bg-gradient-to-r from-primary/20 to-secondary/20 flex items-center justify-center">
                        <i class="fas fa-image text-4xl text-base-content/30"></i>
                    </div>
                @endif

                <!-- Edit Profile Button -->
                <a href="{{ route('web.profile.edit') }}" class="absolute top-4 right-4 rtl:right-auto rtl:left-4 btn btn-sm btn-primary">
                    <i class="fas fa-edit mr-1 rtl:ml-1 rtl:mr-0"></i> {{ __('تعديل الملف') }}
                </a>
            </div>

            <!-- Profile Info -->
            <div class="relative px-6 pt-16 pb-6">
                <!-- Avatar -->
                <div class="absolute -top-12 left-1/2 transform -translate-x-1/2">
                    <div class="avatar">
                        <div class="w-24 h-24 rounded-full ring ring-primary ring-offset-base-100 ring-offset-2">
                            @if($user->sessions->count() > 0 && $user->avatar())
                                <img src="{{ $user->avatar() }}" alt="{{ $user->name }}">
                            @else
                                <img src="{{ asset('assets/default-avatar.png') }}" alt="{{ $user->name }}">
                            @endif
                        </div>
                    </div>
                </div>

                <!-- User Info -->
                <div class="text-center mb-6">
                    <h1 class="text-2xl font-bold">{{ $user->name }}</h1>
                    <p class="text-base-content/70">{{ __('عضو منذ') }} {{ $user->created_at->format('Y-m-d') }}</p>

                    <!-- Email Verification Status -->
                    <div class="flex flex-wrap justify-center gap-2 mt-3">
                        @if($user->email_verified_at)
                            <div class="badge badge-success gap-2">
                                <i class="fas fa-check-circle"></i> {{ __('البريد الإلكتروني مؤكد') }}
                            </div>
                        @else
                            <div class="badge badge-warning gap-2">
                                <i class="fas fa-exclamation-circle"></i> {{ __('البريد الإلكتروني غير مؤكد') }}
                            </div>
                        @endif

                        <!-- Verification Status -->
                        @if($user->isVerified())
                            <div class="badge badge-primary gap-2">
                                <i class="fas fa-shield-check"></i> {{ __('حساب موثق') }}
                            </div>
                        @elseif($user->hasPendingVerification())
                            <div class="badge badge-info gap-2">
                                <i class="fas fa-clock"></i> {{ __('طلب التوثيق قيد المراجعة') }}
                            </div>
                        @else
                            <div class="badge badge-ghost gap-2">
                                <i class="fas fa-shield-alt"></i> {{ __('غير موثق') }}
                            </div>
                        @endif
                    </div>

                    <!-- Verification Request Button -->
                    @if(!$user->isVerified() && !$user->hasPendingVerification() && $user->email_verified_at)
                        <div class="mt-4">
                            <a href="{{ route('web.verification.form') }}" class="btn btn-outline btn-primary btn-sm gap-2">
                                <i class="fas fa-shield-check"></i>
                                {{ __('طلب التوثيق') }}
                            </a>
                            <p class="text-xs text-base-content/60 mt-1">{{ __('احصل على علامة التوثيق لزيادة الثقة في حسابك') }}</p>
                        </div>
                    @elseif(!$user->email_verified_at && !$user->isVerified())
                        <div class="mt-4">
                            <p class="text-xs text-base-content/60">{{ __('يجب تأكيد البريد الإلكتروني أولاً لطلب التوثيق') }}</p>
                        </div>
                    @endif
                </div>

                <!-- Stats -->
                <div class="flex flex-wrap justify-center gap-8 mb-6">
                    <a href="{{ route('web.profile.products') }}" class="stat-box">
                        <div class="stat-figure text-primary">
                            <i class="fas fa-tag text-2xl"></i>
                        </div>
                        <div class="stat-title">{{ __('إعلاناتي') }}</div>
                        <div class="stat-value">{{ $user->products->count() }}</div>
                    </a>

                    <a href="{{ route('web.profile.favorites') }}" class="stat-box">
                        <div class="stat-figure text-primary">
                            <i class="fas fa-heart text-2xl"></i>
                        </div>
                        <div class="stat-title">{{ __('المفضلة') }}</div>
                        <div class="stat-value">{{ $user->favorites->count() }}</div>
                    </a>

                    <a href="{{ route('web.profile.followed_products') }}" class="stat-box">
                        <div class="stat-figure text-primary">
                            <i class="fas fa-bell text-2xl"></i>
                        </div>
                        <div class="stat-title">{{ __('منتجات أتابعها') }}</div>
                        <div class="stat-value">{{ $user->follows()->where('followable_type', 'App\\Models\\Product')->count() }}</div>
                    </a>

                    <a href="{{ route('web.profile.followers') }}" class="stat-box">
                        <div class="stat-figure text-primary">
                            <i class="fas fa-users text-2xl"></i>
                        </div>
                        <div class="stat-title">{{ __('المتابعين') }}</div>
                        <div class="stat-value">{{ $user->followers->count() }}</div>
                    </a>

                    <a href="{{ route('web.profile.following') }}" class="stat-box">
                        <div class="stat-figure text-primary">
                            <i class="fas fa-user-plus text-2xl"></i>
                        </div>
                        <div class="stat-title">{{ __('أتابعهم') }}</div>
                        <div class="stat-value">{{ $user->follows()->where('followable_type', 'App\\Models\\User')->count() }}</div>
                    </a>
                </div>

                <!-- Contact Info -->
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div class="flex items-center">
                        <i class="fas fa-envelope mr-3 rtl:ml-3 rtl:mr-0 text-primary"></i>
                        <span>{{ $user->email }}</span>
                    </div>

                    @if($user->phone)
                        <div class="flex items-center">
                            <i class="fas fa-phone-alt mr-3 rtl:ml-3 rtl:mr-0 text-primary"></i>
                            <span>{{ $user->phone }}</span>
                        </div>
                    @endif

                    @if($user->sessions->count() > 0 && $user->sessions->first()->bio)
                        <div class="col-span-1 md:col-span-2 mt-4">
                            <h2 class="text-lg font-bold mb-2">{{ __('نبذة') }}</h2>
                            <p>{{ $user->sessions->first()->bio }}</p>
                        </div>
                    @endif
                </div>
            </div>
        </div>

        <!-- Recent Chats -->
        <div class="bg-base-100 rounded-lg shadow-md p-6 mb-8">
            <div class="flex justify-between items-center mb-6">
                <h2 class="text-xl font-bold">{{ __('المحادثات الأخيرة') }}</h2>
                <a href="{{ route('web.chat.index') }}" class="btn btn-sm btn-outline btn-primary">
                    {{ __('عرض الكل') }}
                </a>
            </div>

            @php
                $recentChats = \Modules\Chat\app\Models\Chat::where('sender_id', $user->id)
                    ->orWhere('receiver_id', $user->id)
                    ->with(['sender', 'receiver', 'lastMessage'])
                    ->orderBy('last_message_at', 'desc')
                    ->limit(3)
                    ->get();
            @endphp

            @if($recentChats->count() > 0)
                <div class="grid grid-cols-1 gap-4">
                    @foreach($recentChats as $chat)
                        @php
                            $otherUser = $chat->sender_id == $user->id ? $chat->receiver : $chat->sender;
                            $unreadCount = $chat->messages->where('user_id', '!=', $user->id)->where('is_read', false)->count();
                        @endphp
                        <a href="{{ route('web.chat.show', $chat->id) }}" class="flex items-center p-4 rounded-lg border border-base-300 hover:bg-base-200 transition-colors">
                            <div class="avatar">
                                <div class="w-12 h-12 rounded-full">
                                    <img src="{{ $otherUser->avatar() }}" alt="{{ $otherUser->name }}">
                                </div>
                            </div>
                            <div class="ml-4 rtl:mr-4 rtl:ml-0 flex-1">
                                <div class="flex justify-between items-center">
                                    <h3 class="font-bold">{{ $otherUser->name }}</h3>
                                    <span class="text-sm text-base-content/70">{{ $chat->last_message_at ? $chat->last_message_at->diffForHumans() : $chat->created_at->diffForHumans() }}</span>
                                </div>
                                <p class="text-sm text-base-content/70 truncate max-w-xs">
                                    @if($chat->lastMessage)
                                        {{ $chat->lastMessage->content }}
                                    @else
                                        <span class="italic">{{ __('لا توجد رسائل') }}</span>
                                    @endif
                                </p>
                                @if($chat->product)
                                    <div class="mt-2 flex items-center">
                                        <span class="badge badge-sm">{{ __('منتج') }}: {{ $chat->product->title }}</span>
                                    </div>
                                @endif
                            </div>
                        </a>
                    @endforeach
                </div>
            @else
                <div class="text-center py-8">
                    <i class="far fa-comments text-5xl text-base-content/30 mb-4"></i>
                    <p class="text-lg">{{ __('لا توجد محادثات حتى الآن') }}</p>
                </div>
            @endif
        </div>

        <!-- Recent Products -->
        <div class="bg-base-100 rounded-lg shadow-md p-6 mb-8">
            <div class="flex justify-between items-center mb-6">
                <h2 class="text-xl font-bold">{{ __('إعلاناتي') }}</h2>
                <a href="{{ route('web.profile.products') }}" class="btn btn-sm btn-outline btn-primary">
                    {{ __('عرض الكل') }}
                </a>
            </div>

            @if($user->products->count() > 0)
                <!-- Products Component -->
                @livewire('products', [
                    'pageType' => 'profile',
                    'filters' => [
                        'sort' => 'newest'
                    ],
                    'showCategories' => false,
                    'showFilters' => false
                ])
            @else
                <div class="text-center py-8">
                    <i class="fas fa-tag text-5xl text-base-content/30 mb-4"></i>
                    <p class="text-lg">{{ __('لا توجد إعلانات حتى الآن') }}</p>
                    @if($user->hasVerifiedEmail())
                        <a href="{{ route('web.products.create') }}" class="btn btn-primary mt-4">
                            <i class="fas fa-plus mr-2 rtl:ml-2 rtl:mr-0"></i> {{ __('إضافة إعلان جديد') }}
                        </a>
                    @else
                        <a href="{{ route('web.auth.verification.notice') }}" class="btn btn-warning mt-4">
                            <i class="fas fa-exclamation-triangle mr-2 rtl:ml-2 rtl:mr-0"></i> {{ __('تحقق من البريد لإضافة إعلان') }}
                        </a>
                    @endif
                </div>
            @endif
        </div>
    </div>
</main>
@endsection

@push('styles')
<style>
    .stat-box {
        @apply bg-base-200 rounded-lg p-4 text-center transition-all duration-300 hover:bg-primary/10;
    }
</style>
@endpush
