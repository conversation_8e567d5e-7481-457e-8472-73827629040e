@extends('web.layouts.layout')
@section('title', __('إعلاناتي'))

@section('page-meta')
    <meta name="description" content="{{ __('إدارة إعلاناتي في موقع حراجي') }}">
    <meta name="keywords" content="{{ __('حراجي, إعلانات, بيع, شراء, إدارة الإعلانات') }}">
@endsection

@section('content')
    <main class="py-8">
        <div class="container mx-auto px-4">
            <!-- Breadcrumbs -->
            <div class="text-sm breadcrumbs mb-6">
                <ul>
                    <li><a href="{{ route('web.index') }}">{{ __('الرئيسية') }}</a></li>
                    <li><a href="{{ route('web.profile.show') }}">{{ __('الملف الشخصي') }}</a></li>
                    <li>{{ __('إعلاناتي') }}</li>
                </ul>
            </div>

            <!-- Products Management -->
            <div class="bg-base-100 rounded-lg shadow-md p-6 mb-8">
                <div class="flex justify-between items-center mb-6">
                    <h1 class="text-2xl font-bold">{{ __('إعلاناتي') }}</h1>

                    @if (auth()->user()->hasVerifiedEmail())
                        <a href="{{ route('web.products.create') }}" class="btn btn-primary">
                            <i class="fas fa-plus mr-2 rtl:ml-2 rtl:mr-0"></i> {{ __('إضافة إعلان جديد') }}
                        </a>
                    @else
                        <a href="{{ route('web.auth.verification.notice') }}" class="btn btn-warning">
                            <i class="fas fa-exclamation-triangle mr-2 rtl:ml-2 rtl:mr-0"></i>
                            {{ __('تحقق من البريد لإضافة إعلان') }}
                        </a>
                    @endif
                </div>

                @if (session('success'))
                    <div class="alert alert-success mb-6">
                        <i class="fas fa-check-circle"></i> {{ session('success') }}
                    </div>
                @endif

                @if (session('error'))
                    <div class="alert alert-error mb-6">
                        <i class="fas fa-exclamation-circle"></i> {{ session('error') }}
                    </div>
                @endif

                @if ($products->count() > 0)
                    <!-- Desktop Table View (Hidden on Mobile) -->
                    <div class="overflow-x-auto hidden md:block">
                        <table class="table w-full">
                            <thead>
                                <tr>
                                    <th>{{ __('الصورة') }}</th>
                                    <th>{{ __('العنوان') }}</th>
                                    <th>{{ __('السعر') }}</th>
                                    <th>{{ __('الحالة') }}</th>
                                    <th>{{ __('المشاهدات') }}</th>
                                    <th>{{ __('التاريخ') }}</th>
                                    <th>{{ __('الإجراءات') }}</th>
                                </tr>
                            </thead>
                            <tbody>
                                @foreach ($products as $product)
                                    <tr>
                                        <td>
                                            <div class="avatar">
                                                <div class="w-16 h-16 rounded">
                                                    @if ($product->primaryMediaUrl())
                                                        <img src="{{ $product->primaryMediaUrl() }}"
                                                            alt="{{ $product->title }}">
                                                    @else
                                                        <div
                                                            class="w-full h-full bg-base-200 flex items-center justify-center">
                                                            <i class="fas fa-image text-base-content/30"></i>
                                                        </div>
                                                    @endif
                                                </div>
                                            </div>
                                        </td>
                                        <td>
                                            <a href="{{ route('web.product', $product->slug) }}"
                                                class="font-bold hover:text-primary">
                                                {{ \Illuminate\Support\Str::limit($product->title, 30) }}
                                            </a>
                                            <div class="text-sm text-base-content/70">
                                                {{ $product->category->name }}
                                            </div>
                                        </td>
                                        <td>{{ number_format($product->price, 2) }} {{ __('ريال') }}</td>
                                        <td>
                                            @if ($product->status == 'active' || $product->status == 'approved')
                                                <span class="badge badge-success">{{ __('نشط') }}</span>
                                            @elseif($product->status == 'pending')
                                                <span class="badge badge-warning">{{ __('قيد المراجعة') }}</span>
                                            @elseif($product->status == 'rejected')
                                                <span class="badge badge-error">{{ __('مرفوض') }}</span>
                                            @elseif($product->status == 'sold')
                                                <span class="badge badge-info">{{ __('تم البيع') }}</span>
                                            @else
                                                <span class="badge">{{ $product->status }}</span>
                                            @endif
                                        </td>
                                        <td>{{ $product->views_count }}</td>
                                        <td>{{ $product->created_at->format('Y-m-d') }}</td>
                                        <td>
                                            <div class="flex gap-2">
                                                <a href="{{ route('web.product', $product->slug) }}"
                                                    class="btn btn-sm btn-ghost btn-circle" title="{{ __('عرض') }}">
                                                    <i class="fas fa-eye"></i>
                                                </a>
                                                <a href="{{ route('web.editProduct', $product->id) }}"
                                                    class="btn btn-sm btn-ghost btn-circle" title="{{ __('تعديل') }}">
                                                    <i class="fas fa-edit"></i>
                                                </a>
                                                
                                                 <!-- check if contain -->
                                                  @if(\Illuminate\Support\Str::contains($product->status->name, 'APPROVED') || $product->status->name == 'ACTIVE')
                                                    <div class="dropdown dropdown-end">
                                                        <label tabindex="0" class="btn btn-sm btn-ghost btn-circle"
                                                            title="{{ __('تغيير الحالة') }}">
                                                            <i class="fas fa-ellipsis-v"></i>
                                                        </label>
                                                        <ul tabindex="0"
                                                            class="dropdown-content z-[1] menu p-2 shadow bg-base-100 rounded-box w-52">
                                                            <li>
                                                                <button type="button" class="text-info"
                                                                    onclick="confirmStatusChange('{{ $product->id }}', 'sold', '{{ __('تم البيع') }}')">
                                                                    <i class="fas fa-check-circle mr-2"></i>
                                                                    {{ __('تم البيع') }}
                                                                </button>
                                                            </li>
                                                            <li>
                                                                <button type="button" class="text-error"
                                                                    onclick="confirmStatusChange('{{ $product->id }}', 'delete', '{{ __('حذف') }}')">
                                                                    <i class="fas fa-trash mr-2"></i>
                                                                    {{ __('حذف') }}
                                                                </button>
                                                            </li>
                                                        </ul>
                                                    </div>
                                                @endif
                                                @if (in_array($product->status, ['sold', 'approved']) && !$product->hasCommission())
                                                    <button type="button" class="btn btn-sm btn-warning btn-circle"
                                                        title="{{ __('دفع العمولة') }}"
                                                        onclick="openCommissionModal('{{ $product->id }}', '{{ $product->title }}', '{{ $product->price ?? 0 }}')">
                                                        <i class="fas fa-dollar-sign"></i>
                                                    </button>
                                                @endif
                                            </div>
                                        </td>
                                    </tr>
                                @endforeach
                            </tbody>
                        </table>
                    </div>

                    <!-- Mobile Card View (Shown only on Mobile) -->
                    <div class="grid grid-cols-1 gap-4 md:hidden">
                        @foreach ($products as $product)
                            <div class="card bg-base-200 shadow-sm">
                                <div class="card-body p-4">
                                    <div class="flex items-start gap-3">
                                        <!-- Product Image -->
                                        <div class="avatar">
                                            <div class="w-20 h-20 rounded">
                                                @if ($product->primaryMediaUrl())
                                                    <img src="{{ $product->primaryMediaUrl() }}"
                                                        alt="{{ $product->title }}">
                                                @else
                                                    <div class="w-full h-full bg-base-300 flex items-center justify-center">
                                                        <i class="fas fa-image text-base-content/30"></i>
                                                    </div>
                                                @endif
                                            </div>
                                        </div>

                                        <!-- Product Info -->
                                        <div class="flex-1">
                                            <h3 class="font-bold text-base mb-1">
                                                <a href="{{ route('web.product', $product->slug) }}"
                                                    class="hover:text-primary">
                                                    {{ \Illuminate\Support\Str::limit($product->title, 40) }}
                                                </a>
                                            </h3>
                                            <div class="text-sm text-base-content/70 mb-1">
                                                {{ $product->category->name }}
                                            </div>
                                            <div class="text-base font-semibold mb-1">
                                                {{ number_format($product->price, 2) }} {{ __('ريال') }}
                                            </div>
                                            <div class="flex items-center gap-2 text-sm">
                                                <div>
                                                    @if ($product->status == 'active')
                                                        <span
                                                            class="badge badge-success badge-sm">{{ __('نشط') }}</span>
                                                    @elseif($product->status == 'pending')
                                                        <span
                                                            class="badge badge-warning badge-sm">{{ __('قيد المراجعة') }}</span>
                                                    @elseif($product->status == 'rejected')
                                                        <span class="badge badge-error badge-sm">{{ __('مرفوض') }}</span>
                                                    @elseif($product->status == 'sold')
                                                        <span class="badge badge-info badge-sm">{{ __('تم البيع') }}</span>
                                                    @else
                                                        <span class="badge badge-sm">{{ $product->status }}</span>
                                                    @endif
                                                </div>
                                                <div class="text-base-content/70">
                                                    <i class="fas fa-eye text-xs"></i> {{ $product->views_count }}
                                                </div>
                                                <div class="text-base-content/70">
                                                    <i class="fas fa-calendar-alt text-xs"></i>
                                                    {{ $product->created_at->format('Y-m-d') }}
                                                </div>

                                    <!-- Actions -->
                                    <div class="card-actions justify-end mt-3 pt-3 border-t border-base-300">
                                        <a href="{{ route('web.product', $product->slug) }}" class="btn btn-sm btn-ghost"
                                            title="{{ __('عرض') }}">
                                            <i class="fas fa-eye mr-1"></i> {{ __('عرض') }}
                                        </a>
                                        <a href="{{ route('web.editProduct', $product->id) }}" class="btn btn-sm btn-ghost"
                                            title="{{ __('تعديل') }}">
                                            <i class="fas fa-edit mr-1"></i> {{ __('تعديل') }}
                                        </a>
                                        @if ($product->status == 'active' || $product->status == 'approved')
                                            <div class="dropdown dropdown-top dropdown-end">
                                                <label tabindex="0" class="btn btn-sm btn-ghost"
                                                    title="{{ __('تغيير الحالة') }}">
                                                    <i class="fas fa-ellipsis-v mr-1"></i> {{ __('الحالة') }}
                                                </label>
                                                <ul tabindex="0"
                                                    class="dropdown-content z-[1] menu p-2 shadow bg-base-100 rounded-box w-52">
                                                    <li>
                                                        <button type="button" class="text-info"
                                                            onclick="confirmStatusChange('{{ $product->id }}', 'sold', '{{ __('تم البيع') }}')">
                                                            <i class="fas fa-check-circle mr-2"></i>
                                                            {{ __('تم البيع') }}
                                                        </button>
                                                    </li>
                                                    <li>
                                                        <button type="button" class="text-error"
                                                            onclick="confirmStatusChange('{{ $product->id }}', 'delete', '{{ __('حذف') }}')">
                                                            <i class="fas fa-trash mr-2"></i> {{ __('حذف') }}
                                                        </button>
                                                    </li>
                                                </ul>
                                            </div>
                                        @endif
                                        @if (in_array($product->status, ['sold', 'approved']) && !$product->hasCommission())
                                            <button type="button" class="btn btn-sm btn-warning"
                                                onclick="openCommissionModal('{{ $product->id }}', '{{ $product->title }}', '{{ $product->price ?? 0 }}')">
                                                <i class="fas fa-dollar-sign mr-1"></i> {{ __('دفع العمولة') }}
                                            </button>
                                        @endif
                                    </div>
                                            </div>
                                        </div>
                                    </div>

                                </div>
                            </div>
                        @endforeach
                    </div>

                    <!-- Pagination -->
                    <div class="mt-6">
                        {{ $products->links() }}
                    </div>
                @else
                    <div class="text-center py-8">
                        <i class="fas fa-tag text-5xl text-base-content/30 mb-4"></i>
                        <p class="text-lg">{{ __('لا توجد إعلانات حتى الآن') }}</p>
                        @if (auth()->user()->hasVerifiedEmail())
                            <a href="{{ route('web.products.create') }}" class="btn btn-primary mt-4">
                                <i class="fas fa-plus mr-2 rtl:ml-2 rtl:mr-0"></i> {{ __('إضافة إعلان جديد') }}
                            </a>
                        @else
                            <a href="{{ route('web.auth.verification.notice') }}" class="btn btn-warning mt-4">
                                <i class="fas fa-exclamation-triangle mr-2 rtl:ml-2 rtl:mr-0"></i>
                                {{ __('تحقق من البريد لإضافة إعلان') }}
                            </a>
                        @endif
                    </div>
                @endif
            </div>
        </div>
    </main>

    <!-- Commission Payment Modal -->
    <div id="commissionModal" class="modal">
        <div class="modal-box w-11/12 max-w-2xl">
            <h3 class="font-bold text-lg mb-4">{{ __('دفع عمولة المنتج') }}</h3>

            <form id="commissionForm" action="" method="POST" enctype="multipart/form-data">
                @csrf

                <!-- Product Info -->
                <div class="bg-base-200 p-4 rounded-lg mb-4">
                    <h4 class="font-semibold mb-2">{{ __('معلومات المنتج') }}</h4>
                    <p id="productTitle" class="text-sm"></p>
                </div>

                <!-- Sell Price -->
                <div class="form-control mb-4">
                    <label class="label">
                        <span class="label-text">{{ __('سعر البيع') }} <span class="text-error">*</span></span>
                    </label>
                    <input type="number" id="sellPrice" name="sell_price" step="0.01" min="0"
                           class="input input-bordered" placeholder="{{ __('أدخل سعر البيع الفعلي') }}" required>
                    <label class="label">
                        <span class="label-text-alt text-info">{{ __('سيتم حساب العمولة بناءً على هذا السعر (1%)') }}</span>
                    </label>
                </div>

                <!-- Commission Display -->
                <div class="bg-warning/10 border border-warning/20 p-4 rounded-lg mb-4">
                    <div class="flex justify-between items-center">
                        <span class="font-semibold">{{ __('العمولة المطلوبة (1%)') }}:</span>
                        <span id="commissionAmount" class="text-lg font-bold text-warning">0.00 {{ __('ريال') }}</span>
                    </div>
                </div>

                <!-- Payment Method -->
                <div class="form-control mb-4">
                    <label class="label">
                        <span class="label-text">{{ __('طريقة الدفع') }} <span class="text-error">*</span></span>
                    </label>
                    <select id="paymentMethod" name="payment_method" class="select select-bordered" required>
                        <option value="">{{ __('اختر طريقة الدفع') }}</option>
                        <option value="wallet">{{ __('محفظة إلكترونية') }}</option>
                        <option value="bank_transfer">{{ __('تحويل بنكي') }}</option>
                        <option value="online_payment">{{ __('دفع إلكتروني') }}</option>
                    </select>
                </div>

                <!-- Payment Proof (shown for wallet and bank_transfer) -->
                <div id="paymentProofSection" class="form-control mb-4" style="display: none;">
                    <label class="label">
                        <span class="label-text">{{ __('إثبات الدفع') }} <span class="text-error">*</span></span>
                    </label>
                    <input type="file" name="payment_proof" class="file-input file-input-bordered"
                           accept="image/jpeg,image/png,image/jpg,image/gif">
                    <label class="label">
                        <span class="label-text-alt">{{ __('يرجى رفع صورة إثبات الدفع (JPEG, PNG, JPG, GIF - حد أقصى 2MB)') }}</span>
                    </label>
                </div>

                <!-- Notes -->
                <div class="form-control mb-6">
                    <label class="label">
                        <span class="label-text">{{ __('ملاحظات إضافية') }}</span>
                    </label>
                    <textarea name="notes" class="textarea textarea-bordered" rows="3"
                              placeholder="{{ __('أي ملاحظات إضافية حول الدفع...') }}"></textarea>
                </div>

                <!-- Actions -->
                <div class="modal-action">
                    <button type="button" class="btn" onclick="closeCommissionModal()">{{ __('إلغاء') }}</button>
                    <button type="submit" class="btn btn-primary">{{ __('تقديم طلب الدفع') }}</button>
                </div>
            </form>
        </div>
    </div>

    <!-- Hidden forms for status changes -->
    <form id="statusChangeForm" method="POST" style="display: none;">
        @csrf
        @method('PUT')
    </form>

    <form id="deleteForm" method="POST" style="display: none;">
        @csrf
        @method('DELETE')
    </form>

@endsection

@push('scripts')
<script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
<script>
// Status change confirmation
function confirmStatusChange(productId, action, actionText) {
    const title = action === 'delete' ? 'هل أنت متأكد من حذف هذا الإعلان؟' : 'هل أنت متأكد من تغيير حالة الإعلان؟';
    const text = 'لن تتمكن من التراجع عن هذا الإجراء!';
    const confirmButtonText = actionText;

    Swal.fire({
        title: title,
        text: text,
        icon: 'warning',
        showCancelButton: true,
        confirmButtonColor: action === 'delete' ? '#ef4444' : '#3b82f6',
        cancelButtonColor: '#6b7280',
        confirmButtonText: confirmButtonText,
        cancelButtonText: 'إلغاء',
        reverseButtons: true
    }).then((result) => {
        if (result.isConfirmed) {
            if (action === 'delete') {
                const form = document.getElementById('deleteForm');
                form.action = `/products/${productId}`;
                form.submit();
            } else if (action === 'sold') {
                const form = document.getElementById('statusChangeForm');
                form.action = `/products/${productId}/mark-sold`;
                form.submit();
            }
        }
    });
}

// Commission modal functions
function openCommissionModal(productId, productTitle, productPrice) {
    const modal = document.getElementById('commissionModal');
    const form = document.getElementById('commissionForm');
    const titleElement = document.getElementById('productTitle');
    const priceInput = document.getElementById('sellPrice');

    // Set form action
    form.action = `/products/${productId}/commission`;

    // Set product info
    titleElement.textContent = productTitle;

    // Set default price if available
    if (productPrice && productPrice > 0) {
        priceInput.value = productPrice;
        calculateCommission();
    }

    // Show modal
    modal.classList.add('modal-open');
}

function closeCommissionModal() {
    const modal = document.getElementById('commissionModal');
    modal.classList.remove('modal-open');

    // Reset form
    document.getElementById('commissionForm').reset();
    document.getElementById('commissionAmount').textContent = '0.00 ريال';
    document.getElementById('paymentProofSection').style.display = 'none';
}

// Calculate commission when price changes
document.getElementById('sellPrice').addEventListener('input', calculateCommission);

function calculateCommission() {
    const price = parseFloat(document.getElementById('sellPrice').value) || 0;
    const commission = (price * 0.01).toFixed(2);
    document.getElementById('commissionAmount').textContent = commission + ' ريال';
}

// Show/hide payment proof section based on payment method
document.getElementById('paymentMethod').addEventListener('change', function() {
    const paymentProofSection = document.getElementById('paymentProofSection');
    const paymentProofInput = paymentProofSection.querySelector('input[type="file"]');

    if (this.value === 'wallet' || this.value === 'bank_transfer') {
        paymentProofSection.style.display = 'block';
        paymentProofInput.required = true;
    } else {
        paymentProofSection.style.display = 'none';
        paymentProofInput.required = false;
    }
});

// Close modal when clicking outside
document.getElementById('commissionModal').addEventListener('click', function(e) {
    if (e.target === this) {
        closeCommissionModal();
    }
});

// Form submission handling
document.getElementById('commissionForm').addEventListener('submit', function(e) {
    e.preventDefault();

    const formData = new FormData(this);
    const submitButton = this.querySelector('button[type="submit"]');
    const originalText = submitButton.textContent;

    // Disable submit button
    submitButton.disabled = true;
    submitButton.textContent = 'جاري الإرسال...';

    fetch(this.action, {
        method: 'POST',
        body: formData,
        headers: {
            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            Swal.fire({
                title: 'تم بنجاح!',
                text: data.message,
                icon: 'success',
                confirmButtonText: 'موافق'
            }).then(() => {
                closeCommissionModal();
                location.reload();
            });
        } else {
            Swal.fire({
                title: 'خطأ!',
                text: data.message || 'حدث خطأ أثناء معالجة الطلب',
                icon: 'error',
                confirmButtonText: 'موافق'
            });
        }
    })
    .catch(error => {
        console.error('Error:', error);
        Swal.fire({
            title: 'خطأ!',
            text: 'حدث خطأ أثناء معالجة الطلب',
            icon: 'error',
            confirmButtonText: 'موافق'
        });
    })
    .finally(() => {
        // Re-enable submit button
        submitButton.disabled = false;
        submitButton.textContent = originalText;
    });
});
</script>
@endpush
