<?php

use App\Events\NotificationsEvent;
use App\Http\Controllers\AppNotificationController;
use App\Http\Controllers\AppUpdateController;
use App\Http\Controllers\CategoryController;
use App\Http\Controllers\FaqsController;
use App\Http\Controllers\FileManagerController;
use App\Http\Controllers\language\LanguageController;
use App\Http\Controllers\pages\HomeController;
use App\Http\Controllers\ProfileController;
use App\Http\Controllers\RoleController;
use App\Http\Controllers\SettingsController;
use App\Http\Controllers\UserController;
use App\Http\Controllers\ShortUrlController;
use App\Http\Controllers\PageController;
use App\Http\Controllers\QueueController;
use Illuminate\Support\Facades\Route;
use Modules\TrafficLogs\controller\TrafficLogsController;
use Modules\Blog\Controller\BlogCategoryController;
use Modules\Blog\Controller\BlogController;
use App\Http\Controllers\ProductController;
use App\Http\Controllers\LocationController;
use App\Http\Controllers\TagController;
use App\Http\Controllers\BrandController;
use App\Http\Controllers\AchievementController;
use App\Http\Controllers\CommissionController;
use App\Http\Controllers\UserCommissionController;

use App\Http\Controllers\BannerAdController;
use App\Http\Controllers\CategoryAttributeController;
use App\Http\Controllers\ReportController;
use App\Http\Controllers\UserRateController;
use Modules\UserTracker\app\Http\Middleware\TrackUserActivity;
use App\Http\Controllers\AppMenuController;
use App\Http\Controllers\front\Front2Controller;
use App\Http\Controllers\front\Front2AuthController;
use App\Http\Middleware\TrackUserSession;
use Spatie\Sitemap\Sitemap;
use Spatie\Sitemap\Tags\Url;

Route::get('/lang/{locale}', [LanguageController::class, 'swap']);


Route::middleware(TrackUserSession::class)->name('web.')->group(function () {

        // Auth routes
        Route::prefix('auth')->name('auth.')->group(function () {
            Route::controller(Front2AuthController::class)->group(function () {
                // Guest routes
                Route::middleware('guest')->group(function () {
                    Route::get('/login', 'showLoginForm')->name('login');
                    Route::post('/login', 'login');
                    Route::get('/register', 'showRegistrationForm')->name('register');
                    Route::post('/register', 'register');
                    Route::get('/forgot-password', 'showForgotPasswordForm')->name('forgot-password');
                    Route::post('/forgot-password', 'sendResetLinkEmail')->name('forgot-password');
                    Route::get('/reset-password/{token}', 'showResetPasswordForm')->name('password.reset');
                    Route::post('/reset-password', 'resetPassword')->name('reset-password');
                });

                // Auth routes
                Route::middleware('web.auth')->group(function () {
                    Route::post('/logout', 'logout')->name('logout');
                    Route::get('/email/verify', 'verificationNotice')->name('verification.notice');
                    Route::post('/email/verification-notification', 'sendVerificationEmail')->name('verification.send');
                });

                // Verification route (special case)
                Route::get('/email/verify/{id}/{hash}', 'verifyEmail')
                    ->middleware(['signed', 'throttle:6,1'])
                    ->name('verification.verify');
            });
        });

        // Main front2 routes
        Route::controller(Front2Controller::class)->group(function () {
            // Public routes
            Route::get('/', 'index')->name('index');
            Route::get('/locale/{locale}', 'setLocale')->name('setLocale');
            Route::get('/theme/{theme}', 'setTheme')->name('setTheme');
            Route::get('/categories', 'categories')->name('categories');
            Route::get('/category/{slug}', 'category')->name('category');
            Route::get('/search', 'search')->name('search');
            Route::get('/product/{slug}', 'product')->name('product');
            Route::get('/seller/{username}', 'sellerProfile')->name('sellerProfile');
            Route::get('/about', 'about')->name('about');
            Route::get('/contact', 'contactUs')->name('contactUs');
            Route::post('/newsletter/subscribe', 'subscribeNewsletter')->name('newsletter.subscribe');

            // Auth required routes
            Route::middleware(['web.auth'])->group(function () {
                Route::get('/favorites', 'favorites')->name('favorites');
                Route::post('/favorites/toggle/{productId}', 'toggleFavorite')->name('toggleFavorite');
                Route::post('/product/{product}/comment', 'addComment')->name('product.comment');
                Route::get('/report/{productId}', 'reportProduct')->name('report');
                Route::post('/report/{productId}', 'submitReport')->name('submitReport');
                Route::post('/report', 'storeReport')->name('report.store');

                // User profile routes
                Route::get('/profile', 'profileShow')->name('profile.show');
                Route::get('/profile/edit', 'profileEdit')->name('profile.edit');
                Route::put('/profile', 'profileUpdate')->name('profile.update');
                Route::get('/profile/products', 'profileProducts')->name('profile.products');
                Route::get('/profile/favorites', 'profileFavorites')->name('profile.favorites');
                Route::get('/profile/followers', 'profileFollowers')->name('profile.followers');
                Route::get('/profile/following', 'profileFollowing')->name('profile.following');
                Route::get('/profile/followed-products', 'profileFollowedProducts')->name('profile.followed_products');
                Route::post('/profile/toggle-follow', 'toggleFollow')->name('toggle.follow');
                Route::post('/profile/toggle-favorite/{productId}', 'profileToggleFavorite')->name('profileToggleFavorite');

                // User Commission routes
                Route::prefix('commissions')->name('commissions.')->controller(UserCommissionController::class)->group(function () {
                    Route::get('/', 'index')->name('index');
                    Route::get('/product/{product}/create', 'create')->name('create');
                    Route::post('/product/{product}', 'store')->name('store');
                    Route::get('/{commission}', 'show')->name('show');
                    Route::get('/{commission}/edit', 'edit')->name('edit');
                    Route::put('/{commission}', 'update')->name('update');
                    Route::delete('/{commission}', 'destroy')->name('destroy');
                });

                // User verification routes
                Route::get('/verification/request', [\App\Http\Controllers\VerificationController::class, 'showVerificationForm'])->name('verification.form');
                Route::post('/verification/request', [\App\Http\Controllers\VerificationController::class, 'submitVerification'])->name('verification.submit');
                Route::get('/profile/product/{id}/edit', 'editProduct')->name('editProduct');
                Route::put('/profile/product/{id}', 'updateProduct')->name('updateProduct');
                Route::delete('/profile/product/{id}', 'deleteProduct')->name('deleteProduct');
                Route::put('/profile/product/{id}/mark-sold', 'markProductSold')->name('markProductSold');
                Route::put('/profile/product/{id}/mark-deleted', 'markProductDeleted')->name('markProductDeleted');
                Route::post('/products/{productId}/commission', 'createCommission')->name('products.commission');
            });

            // Email verification required routes
            Route::middleware(['web.auth', 'verified'])->group(function () {
                Route::get('/products/create', 'createProduct')->name('products.create');
                Route::post('/products', 'storeProduct')->name('products.store');
            });
        });
    });



    // pages




    Route::get('page/{slug}', [PageController::class, 'open'])->name('page');

Route::prefix('admin')
    ->middleware(['auth', TrackUserActivity::class])
    ->group(function () {
        Route::get('/deleteAllFiles', [HomeController::class, 'deleteAllFiles'])->name('deleteAllFiles');
        Route::get('/homeCreateBook', [HomeController::class, 'testCreateBook'])->name('homeCreateBook');
        Route::get('/dashboard', [HomeController::class, 'index'])->name('dashboard');

        Route::resource('users', UserController::class);
        Route::post('users/{user}', [UserController::class, 'sendNotificationToUser'])->name('users.send.notification');
        Route::resource('roles', RoleController::class);
        Route::resource('categories', CategoryController::class);
        Route::resource('app-updates', AppUpdateController::class);
        Route::resource('app-notifications', AppNotificationController::class);
        Route::resource('pages', PageController::class);
        Route::resource('faqs', FaqsController::class);

        // File Manager Routes
        Route::prefix('files')
            ->name('files.')
            ->group(function () {
                Route::get('/search', [FileManagerController::class, 'search'])->name('search');
                Route::get('/{path?}', [FileManagerController::class, 'index'])
                    ->name('index')
                    ->where('path', '.*');

                Route::post('/store', [FileManagerController::class, 'store'])->name('store');
                Route::post('/store2', [FileManagerController::class, 'store2'])->name('store2');
                Route::delete('/destroy', [FileManagerController::class, 'destroy'])->name('destroy');
                Route::post('/bulk-delete', [FileManagerController::class, 'bulkDelete'])->name('bulk-delete');
                Route::post('/download', [FileManagerController::class, 'download'])->name('download');
                Route::post('/download-directory', [FileManagerController::class, 'downloadDirectory'])->name('download-directory');
                Route::post('/file-details', [FileManagerController::class, 'getFileDetails'])->name('file-details');
                Route::post('/rename', [FileManagerController::class, 'rename'])->name('rename');
                Route::post('/paste/{filename}', [FileManagerController::class, 'paste'])->name('paste');
            });

        Route::prefix('file-manager')
            ->controller(FileManagerController::class)
            ->group(function () {
                Route::get('/files/{path?}', 'index')->where('path', '.*')->name('files.index');
                Route::post('/files/store/{disk?}', 'store')->name('files.store');
                Route::post('/files', 'store2')->name('files.store2');
                // Route::delete('/files/{filename}', 'destroy')->name('files.destroy');
                Route::post('/download/file', 'download')->name('files.download');
            });

        // Marketplace routes
        Route::resource('products', ProductController::class);

        Route::resource('locations', LocationController::class);
        Route::resource('tags', TagController::class);
        Route::resource('brands', BrandController::class);
        Route::resource('achievements', AchievementController::class);

        Route::get('/page/{slug}', [PageController::class, 'open'])->name('page');

        Route::resource('settings', SettingsController::class);

        Route::get('/traffic', [TrafficLogsController::class, 'index'])->name('traffic.index');
        Route::get('/traffic/logs/{id}', [TrafficLogsController::class, 'logs'])->name('traffic.logs');

        Route::get('/profile', [ProfileController::class, 'edit'])->name('profile.edit');
        Route::patch('/profile', [ProfileController::class, 'update'])->name('profile.update');
        Route::delete('/profile', [ProfileController::class, 'destroy'])->name('profile.destroy');
        Route::get('/profile/notifications', [ProfileController::class, 'notifications'])->name('profile.notifications');
        Route::delete('/profile/notifications/{notification}', [ProfileController::class, 'deleteNotification'])->name('profile.notifications.delete');


        //* Short Urls
        Route::get('/short-urls', [ShortUrlController::class, 'index'])->name('short-urls.index');
        Route::post('/short-urls', [ShortUrlController::class, 'create'])->name('short-urls.store');
        Route::put('/short-urls/{shortUrl}', [ShortUrlController::class, 'update'])->name('short-urls.update');
        Route::delete('/ /{shortUrl}', [ShortUrlController::class, 'destroy'])->name('short-urls.destroy');

        //*Blogs
        Route::resource('blog-categories', BlogCategoryController::class);
        Route::resource('blogs', BlogController::class);
        Route::get('/blog/{slug}', [BlogController::class, 'show'])->name('blog');

        //* Queue

        Route::get('/queue/cancel/{id}', [QueueController::class, 'cancelJob'])->name('queue.cancel');
        Route::get('/queue/retry-all', [QueueController::class, 'retryAllFailedJobs']);
        Route::get('/queue/retry/{id}', [QueueController::class, 'retryFailedJob'])->name('queue.retry');
        Route::get('/queue/failed-jobs', [QueueController::class, 'listFailedJobs']);
        Route::get('/queue/flush', [QueueController::class, 'flushFailedJobs']);

        // Category Attributes routes
        Route::prefix('admin/categories/{category}')->name('categories.')->group(function () {
            Route::get('/attributes', [CategoryAttributeController::class, 'index'])->name('attributes.index');
            Route::get('/attributes/create', [CategoryAttributeController::class, 'create'])->name('attributes.create');
            Route::post('/attributes', [CategoryAttributeController::class, 'store'])->name('attributes.store');
            Route::get('/attributes/{attribute}/edit', [CategoryAttributeController::class, 'edit'])->name('attributes.edit');
            Route::put('/attributes/{attribute}', [CategoryAttributeController::class, 'update'])->name('attributes.update');
            Route::delete('/attributes/{attribute}', [CategoryAttributeController::class, 'destroy'])->name('attributes.destroy');
            Route::post('/attributes/update-order', [CategoryAttributeController::class, 'updateOrder'])->name('attributes.update-order');
        });

        // Reports
        Route::resource('reports', ReportController::class);

        Route::resource('app-menus', AppMenuController::class);

        // Banner Ads
        Route::resource('banner-ads', BannerAdController::class);

        // User Rates
        Route::resource('user-rates', UserRateController::class)->except(['create', 'edit', 'update']);
        Route::patch('user-rates/{userRate}/status', [UserRateController::class, 'updateStatus'])->name('user-rates.update-status');
        Route::get('users/{user}/ratings', [UserRateController::class, 'userRatings'])->name('users.ratings');

        // Commissions
        Route::resource('commissions', CommissionController::class);
        Route::post('commissions/{commission}/approve', [CommissionController::class, 'approve'])->name('commissions.approve');
        Route::post('commissions/{commission}/reject', [CommissionController::class, 'reject'])->name('commissions.reject');

        // Verification Management
        Route::prefix('verification')->name('verifications.')->group(function () {
            Route::post('/approve/{verification}', [\App\Http\Controllers\VerificationController::class, 'approve'])->name('approve');
            Route::post('/reject/{verification}', [\App\Http\Controllers\VerificationController::class, 'reject'])->name('reject');
            Route::get('/requirements', [\App\Http\Controllers\VerificationController::class, 'showRequirementsForm'])->name('requirements.show');
            Route::post('/requirements', [\App\Http\Controllers\VerificationController::class, 'storeRequirements'])->name('requirements.store');
            Route::get('/details/{verification}', [\App\Http\Controllers\VerificationController::class, 'getVerificationDetails'])->name('details');
        });

        // API endpoints within admin
        Route::get('api/banner-ads/{screen}', [BannerAdController::class, 'getBannerAdsForScreen']);
    });

Route::get('/srt/{shortCode}', [ShortUrlController::class, 'redirect']);

Route::get('/robots.txt', function () {
    return response("User-agent: *\nDisallow: /", 200, ['Content-Type' => 'text/plain']);
})->name('robots.txt');

require __DIR__ . '/auth.php';
Route::get('/dashboard/refresh', [HomeController::class, 'refreshDashboard'])->name('dashboard.refresh');

// Email verification and password reset pages
Route::get('/email/verify/success', function () {
    return view('auth.verification-success');
})->name('verification.success');

Route::get('/Reset-Pass/{token}', function ($token) {
    return view('auth.reset-password', ['token' => $token]);
})->name('password.reset.form');

Route::get('/password/reset/success', function () {
    return view('auth.reset-success');
})->name('password.reset.success');

Route::get('/test-notification', function () {
    NotificationsEvent::dispatch('Hello World');
    return 'Notification sent';
});


Route::get('/robots.txt', function () {
    $sitemapUrl = config('app.url') . '/sitemap.xml';

    $content = <<<EOT
    User-agent: *
    Disallow: /admin
    Disallow: /telescope
    Disallow: /horizon
    Disallow: /log-viewer
    Disallow: /queue
    Disallow: /profile
    Disallow: /short-urls
    Disallow: /uploads
    Disallow: /srt
    Disallow: /email/verify
    Disallow: /Reset-Pass

    # Allow public routes
    Allow: /
    Allow: /categories
    Allow: /category
    Allow: /search
    Allow: /product
    Allow: /seller
    Allow: /about
    Allow: /contact
    Allow: /blog
    Allow: /page

    Sitemap: {$sitemapUrl}
    EOT;

    return response($content, 200, ['Content-Type' => 'text/plain']);
})->name('robots.txt');

Route::get('/sitemap.xml', function () {
    $sitemap = Sitemap::create()
        // Home page
        ->add(Url::create('/'))

        // Static pages
        ->add(Url::create('/about'))
        ->add(Url::create('/contact'))

        // Marketplace pages
        ->add(Url::create('/categories'))
        ->add(Url::create('/search'))

        // Blog
        ->add(Url::create('/blog'));

    // Add dynamic pages from database
    // Categories
    $categories = \App\Models\Category::where('status', true)->get();
    foreach ($categories as $category) {
        $sitemap->add(Url::create('/category/' . $category->slug));
    }

    // Static pages from database
    $pages = \App\Models\Page::where('status', true)->get();
    foreach ($pages as $page) {
        $sitemap->add(Url::create('/page/' . $page->slug));
    }

    // Blog posts
    if (class_exists('Modules\Blog\Models\Blog')) {
        $blogs = \Modules\Blog\Models\Blog::where('is_published', true)->get();
        foreach ($blogs as $blog) {
            $sitemap->add(Url::create('/blog/' . $blog->slug));
        }
    }

    // Products (limit to approved products, max 1000 for performance)
    $products = \App\Models\Product::where('status', \App\Enums\StatusEnum::APPROVED->value)
        ->orderBy('created_at', 'desc')
        ->limit(1000)
        ->get();
    foreach ($products as $product) {
        $sitemap->add(Url::create('/product/' . $product->slug));
    }

    // Seller profiles (only include users with approved products)
    $sellers = \App\Models\User::whereHas('products', function($query) {
        $query->where('status', \App\Enums\StatusEnum::APPROVED->value);
    })->limit(500)->get();

    foreach ($sellers as $seller) {
        $sitemap->add(Url::create('/seller/' . $seller->username));
    }

    return $sitemap->toResponse(request());
});